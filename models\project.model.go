package models

type ProjectStatus string

const (
	ProjectStatusDraft  ProjectStatus = "DRAFT"
	ProjectStatusActive ProjectStatus = "ACTIVE"
	ProjectStatusClosed ProjectStatus = "CLOSED"
)

type ProjectProviderType string

const (
	ProjectProviderTypeHWC ProjectProviderType = "HWC"
	ProjectProviderTypeAWS ProjectProviderType = "AWS"
	ProjectProviderTypeCHM ProjectProviderType = "CHM"
)

type Project struct {
	BaseModel
	Name            string              `json:"name" gorm:"column:name"`
	Code            string              `json:"code" gorm:"column:code;unique"`
	ContactName     *string             `json:"contact_name" gorm:"column:contact_name"`
	ContactPhone    *string             `json:"contact_phone" gorm:"column:contact_phone"`
	ContactEmail    *string             `json:"contact_email" gorm:"column:contact_email"`
	Budget          *float64            `json:"budget" gorm:"column:budget"`
	Status          ProjectStatus       `json:"status" gorm:"column:status;default:DRAFT"`
	ProviderType    ProjectProviderType `json:"provider_type" gorm:"column:provider_type;default:HWC"`
	AccountName     string              `json:"account_name" gorm:"column:account_name"`
	AccountID       string              `json:"account_id" gorm:"column:account_id"`
	PsaEmail        string              `json:"psa_email" gorm:"column:psa_email"`
	RootAccountName string              `json:"root_account_name" gorm:"column:root_account_name"`
	AccountHolder   string              `json:"account_holder" gorm:"column:account_holder"`
	Remark          string              `json:"remark" gorm:"column:remark"`
	OrganizationID  *string             `json:"organization_id" gorm:"column:organization_id"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id"`

	CreatedBy *User `json:"created_by,omitempty" gorm:"foreignKey:CreatedByID"`
	UpdatedBy *User `json:"updated_by,omitempty" gorm:"foreignKey:UpdatedByID"`
	DeletedBy *User `json:"deleted_by,omitempty" gorm:"foreignKey:DeletedByID"`

	// Project Usage Relations
	Organization  *Organization  `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"`
	ProjectUsage  *ProjectUsage  `json:"project_usage,omitempty" gorm:"foreignKey:ProjectID"`
	ProjectUsages []ProjectUsage `json:"project_usages,omitempty" gorm:"foreignKey:ProjectID"`
	ProjectTags   []ProjectTag   `json:"project_tags,omitempty" gorm:"foreignKey:ProjectID"`
	ProjectItems  []ProjectItem  `json:"project_items,omitempty" gorm:"foreignKey:ProjectID"`
	Cycles        []Cycle        `json:"cycles,omitempty" gorm:"many2many:project_usages;joinForeignKey:ProjectID;joinReferences:CycleID"`
}

func (Project) TableName() string {
	return "projects"
}
