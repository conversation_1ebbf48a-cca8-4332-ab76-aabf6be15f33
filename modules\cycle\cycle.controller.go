package cycle

import (
	"net/http"

	"gitlab.finema.co/finema/csp/csp-api/services"
	core "gitlab.finema.co/finema/idin-core"
)

type CycleController struct {
}

func (m CycleController) Pagination(c core.IHTTPContext) error {
	filters := &services.CycleFilters{
		StartDate: c.<PERSON>("start_date"),
		EndDate:   c.<PERSON>("end_date"),
		Status:    c.Query<PERSON>m("status"),
	}

	cycleSvc := services.NewCycleService(c)
	res, ierr := cycleSvc.Pagination(filters, c.GetPageOptions())
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m CycleController) Find(c core.IHTTPContext) error {
	cycleSvc := services.NewCycleService(c)
	cycle, err := cycleSvc.Find(c.<PERSON>("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.<PERSON>())
	}

	return c.<PERSON><PERSON><PERSON>(http.StatusOK, cycle)
}

func (m CycleController) FindCurrent(c core.IHTTPContext) error {
	cycleSvc := services.NewCycleService(c)
	cycle, err := cycleSvc.FindCurrent()
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, cycle)
}
