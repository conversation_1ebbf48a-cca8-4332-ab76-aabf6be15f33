package project

import (
	"net/http"
	"strings"

	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	"gitlab.finema.co/finema/csp/csp-api/requests"
	"gitlab.finema.co/finema/csp/csp-api/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type ProjectController struct {
}

func (m ProjectController) Pagination(c core.IHTTPContext) error {
	projectSvc := services.NewProjectService(c)

	// Get and validate sort_by parameter
	sortBy := c.QueryParam("sort_by")
	var sortOption repo.ProjectSortOption
	if sortBy != "" {
		sortOption = repo.ProjectSortOption(sortBy)
		// Validate sort option
		if !isValidProjectSortOption(sortOption) {
			return c.JSON(http.StatusBadRequest, map[string]interface{}{
				"code":    "INVALID_SORT_OPTION",
				"message": "Invalid sort option. Valid options are: code_desc, code_asc, budget_desc, usage_desc, usage_percent_desc",
			})
		}
	}

	// Parse tags parameter
	var tags []string
	tagsParam := c.QueryParam("tags")
	if tagsParam != "" {
		// Split comma-separated tag names and trim whitespace
		tagList := strings.Split(tagsParam, ",")
		for _, tag := range tagList {
			trimmedTag := strings.TrimSpace(tag)
			if trimmedTag != "" {
				tags = append(tags, trimmedTag)
			}
		}
	}

	// Get filtering parameters
	filters := &services.ProjectFilters{
		CreatedByID:    c.QueryParam("created_by_id"),
		ProviderType:   models.ProjectProviderType(c.QueryParam("provider_type")),
		Status:         models.ProjectStatus(c.QueryParam("status")),
		AccountHolder:  c.QueryParam("account_holder"),
		OrganizationID: c.QueryParam("organization_id"),
		SortBy:         sortOption,
		Tags:           tags,
	}

	res, ierr := projectSvc.PaginationWithFilters(filters, c.GetPageOptions())

	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m ProjectController) Find(c core.IHTTPContext) error {
	projectSvc := services.NewProjectService(c)
	project, err := projectSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, project)
}

func (m ProjectController) Create(c core.IHTTPContext) error {
	input := &requests.ProjectCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	projectSvc := services.NewProjectService(c)
	payload := &services.ProjectCreatePayload{}
	_ = utils.Copy(payload, input)
	project, err := projectSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, project)
}

func (m ProjectController) Update(c core.IHTTPContext) error {
	input := &requests.ProjectUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	projectSvc := services.NewProjectService(c)
	payload := &services.ProjectUpdatePayload{}
	_ = utils.Copy(payload, input)
	project, err := projectSvc.Update(c.Param("id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, project)
}

func (m ProjectController) Delete(c core.IHTTPContext) error {
	projectSvc := services.NewProjectService(c)
	err := projectSvc.Delete(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}

func (m ProjectController) GetUsages(c core.IHTTPContext) error {
	projectSvc := services.NewProjectService(c)

	// Check if we should filter by current cycle only (default: true)
	currentOnly := true
	if c.QueryParam("all") == "true" {
		currentOnly = false
	}

	res, err := projectSvc.GetUsages(c.Param("id"), currentOnly, c.GetPageOptions())
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m ProjectController) GetUsagesByCycle(c core.IHTTPContext) error {
	projectSvc := services.NewProjectService(c)

	res, err := projectSvc.GetUsagesByCycle(c.Param("id"), c.Param("cycle_id"), c.GetPageOptions())
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m ProjectController) GetCycles(c core.IHTTPContext) error {
	projectSvc := services.NewProjectService(c)

	res, err := projectSvc.GetCycles(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

// isValidProjectSortOption checks if the provided sort option is valid
func isValidProjectSortOption(sortOption repo.ProjectSortOption) bool {
	switch sortOption {
	case repo.ProjectSortCodeDesc, repo.ProjectSortCodeAsc, repo.ProjectSortBudgetDesc,
		repo.ProjectSortUsageDesc, repo.ProjectSortUsagePercentDesc, repo.ProjectSortDefault:
		return true
	default:
		return false
	}
}
