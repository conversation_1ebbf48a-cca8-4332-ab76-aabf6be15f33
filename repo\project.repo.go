package repo

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gorm.io/gorm"
)

type ProjectOption func(repository.IRepository[models.Project])

var Project = func(c core.IContext, options ...ProjectOption) repository.IRepository[models.Project] {
	r := repository.New[models.Project](c)
	for _, opt := range options {
		opt(r)
	}

	return r
}

func ProjectOrderBy(pageOptions *core.PageOptions) ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("code DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}
func ProjectCodeBy(pageOptions *core.PageOptions) ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Order("code DESC")
	}
}

func ProjectWithCreatedBy() ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Preload("CreatedBy")
	}
}

func ProjectWithUpdatedBy() ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Preload("UpdatedBy")
	}
}

func ProjectWithAllRelations() ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Preload("CreatedBy")
		c.Preload("UpdatedBy")
		c.Preload("Organization")
		c.Preload("ProjectTags")
		c.Preload("ProjectItems")
		c.Preload("ProjectUsage", func(db *gorm.DB) *gorm.DB {
			// Get the latest ProjectUsage for current cycle for each project
			return db.Where(`id IN (
				SELECT DISTINCT ON (pu.project_id) pu.id
				FROM project_usages pu
				JOIN cycles c ON pu.cycle_id = c.id
				WHERE c.status = 'current'
				ORDER BY pu.project_id, pu.timestamp DESC
			)`)
		})
		c.Preload("ProjectUsage.Cycle")
	}
}

func ProjectWithProjectUsages() ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Preload("ProjectUsages")
	}
}

func ProjectByCreatedBy(userID string) ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Where("created_by_id = ?", userID)
	}
}

func ProjectByProviderType(providerType models.ProjectProviderType) ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Where("provider_type = ?", providerType)
	}
}

func ProjectByAccountHolder(accountHolderName string) ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Where("account_holder = ?", accountHolderName)
	}
}

func ProjectByOrganization(organizationID string) ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Where("organization_id = ?", organizationID)
	}
}

func ProjectByTags(tagNames []string) ProjectOption {
	if len(tagNames) == 0 {
		return func(c repository.IRepository[models.Project]) {}
	}
	return func(c repository.IRepository[models.Project]) {
		// Use subquery to find projects that have all the specified tags
		c.Where("id IN (SELECT project_id FROM project_tags WHERE name IN ? GROUP BY project_id HAVING COUNT(DISTINCT name) = ?)", tagNames, len(tagNames))
	}
}

func ProjectByStatus(status models.ProjectStatus) ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Where("status = ?", status)
	}
}

func ProjectBySearch(search string) ProjectOption {
	if search == "" {
		return func(c repository.IRepository[models.Project]) {}
	}
	return func(c repository.IRepository[models.Project]) {
		searchPattern := "%" + search + "%"
		c.Joins("LEFT JOIN organizations d ON d.id = projects.organization_id").
			Where(`
				projects.name ILIKE ? 
				OR projects.contact_name ILIKE ? 
				OR projects.contact_email ILIKE ? 
				OR projects.code ILIKE ? 
				OR d.name_th ILIKE ?
				OR d.name_en ILIKE ?
				OR d.short_name_th ILIKE ?
				OR d.short_name_en ILIKE ?
			`,
				searchPattern, searchPattern, searchPattern, searchPattern, searchPattern, searchPattern, searchPattern, searchPattern,
			)
	}
}

// ProjectSortOption represents the available sorting options for projects
type ProjectSortOption string

const (
	// Project Code sorting
	ProjectSortCodeDesc ProjectSortOption = "code_desc" // รหัสโครงการ (ใหม่->เก่า)
	ProjectSortCodeAsc  ProjectSortOption = "code_asc"  // รหัสโครงการ (เก่า->ใหม่)

	// Budget sorting
	ProjectSortBudgetDesc ProjectSortOption = "budget_desc" // Budget (มาก->น้อย)

	// Usage sorting (requires join with project_usage table)
	ProjectSortUsageDesc ProjectSortOption = "usage_desc" // Usage (มาก->น้อย)

	// Usage percentage sorting (requires calculation: usage/budget * 100)
	ProjectSortUsagePercentDesc ProjectSortOption = "usage_percent_desc" // % Usage (มาก->น้อย)

	// Default sorting (fallback)
	ProjectSortDefault ProjectSortOption = "created_at_desc"
)

// ProjectOrderBySortOption creates a ProjectOption for custom sorting
func ProjectOrderBySortOption(sortOption ProjectSortOption) ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		switch sortOption {
		case ProjectSortCodeDesc:
			c.Order("code DESC")
		case ProjectSortCodeAsc:
			c.Order("code ASC")
		case ProjectSortBudgetDesc:
			c.Order("budget DESC NULLS LAST")
		case ProjectSortUsageDesc:
			// Join with project_usage and get latest usage amount
			c.Joins(`LEFT JOIN (
				SELECT project_id, amount as latest_usage_amount
				FROM project_usages pu1
				WHERE pu1.timestamp = (
					SELECT MAX(pu2.timestamp)
					FROM project_usages pu2
					WHERE pu2.project_id = pu1.project_id
				)
			) latest_usage ON projects.id = latest_usage.project_id`).
				Order("latest_usage_amount DESC NULLS LAST")
		case ProjectSortUsagePercentDesc:
			// Join with project_usage and calculate usage percentage
			c.Joins(`LEFT JOIN (
				SELECT project_id, amount as latest_usage_amount
				FROM project_usages pu1
				WHERE pu1.timestamp = (
					SELECT MAX(pu2.timestamp)
					FROM project_usages pu2
					WHERE pu2.project_id = pu1.project_id
				)
			) latest_usage ON projects.id = latest_usage.project_id`).
				Order("CASE WHEN projects.budget > 0 THEN (latest_usage_amount / projects.budget * 100) ELSE 0 END DESC NULLS LAST")
		default:
			c.Order("code DESC")
		}
	}
}
